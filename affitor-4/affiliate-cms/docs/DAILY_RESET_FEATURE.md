# Dual Rate Limiting System (Monthly + Daily for Basic Tiers)

## Overview

This feature implements a dual rate limiting system where basic tier users are subject to BOTH monthly and daily request limits, while other tier users only have monthly limits. The system automatically resets the daily request count at 00:00 UTC each day for basic tier users, while preserving the existing monthly tracking system.

## Affected Subscription Tiers

The daily reset applies only to users with the following subscription tier names:
- `basic-month`
- `basic-quarter` 
- `basic-year`

Users with other subscription tiers (premium, pro, etc.) are **not affected** by daily resets.

## How It Works

### Dual Rate Limiting Logic
- **All users**: Subject to monthly limits using `request_count` and `request_limit`
- **Basic tier users**: Additionally subject to daily limits using `daily_request_count` and `daily_request_limit`
- **Non-basic tier users**: Only have monthly limits (existing behavior unchanged)

### Request Processing
1. When any user makes a request, monthly limit is checked first
2. For basic tier users, daily limit is also checked
3. If either limit is exceeded, the request is denied
4. If both limits allow it, both counters are incremented

### Automatic Daily Reset
- Triggered automatically on the first API request each day for basic tier users
- No background jobs or cron tasks required
- Reset occurs at 00:00 UTC (start of day in UTC timezone)

### Reset Process
1. When a basic tier user makes a request, system checks if daily reset is needed
2. If reset is needed, only `daily_request_count` is set to 0
3. `request_count` (monthly) is preserved and continues accumulating
4. `last_daily_reset_date` is updated to prevent multiple resets per day

### Data Preservation
- Monthly tracking (`request_count`, `request_limit`) is completely preserved
- Only `daily_request_count` is reset to 0 each day
- All statistics and other tracking data are preserved
- `last_request_date` continues to track the most recent request

## Database Changes

### New Fields Added
Three new fields have been added to the `user_tracking_requests` table:

```json
{
  "daily_request_count": {
    "type": "integer",
    "default": 0,
    "min": 0,
    "description": "Daily request count for basic tier users"
  },
  "daily_request_limit": {
    "type": "integer",
    "default": 0,
    "min": 0,
    "description": "Daily request limit for basic tier users"
  },
  "last_daily_reset_date": {
    "type": "datetime",
    "description": "Last date when daily reset was performed for basic tier users"
  }
}
```

### Existing Fields (Unchanged)
- `request_count`: Monthly request count (all users)
- `request_limit`: Monthly request limit (all users)
- All other existing fields remain unchanged

## API Endpoints

### Get User Stats
```
GET /api/user-tracking-request/my-stats
```
Returns current user's tracking statistics including request count, limit, and remaining requests.

### Manual Daily Reset (Admin Only)
```
POST /api/user-tracking-request/manual-daily-reset
Body: { "userId": 123 }
```
Allows administrators to manually trigger a daily reset for testing purposes.

## Implementation Details

### Service Methods

#### `isBasicTierUser(tracking)`
- Checks if a user has a basic tier subscription
- Returns `true` for basic-month, basic-quarter, basic-year tiers
- Returns `false` for all other tiers

#### `getStartOfDayUTC(date)`
- Helper function to get the start of day in UTC timezone
- Ensures consistent date handling across different server timezones

#### `checkAndPerformDailyReset(tracking)`
- Main logic for checking if reset is needed and performing it
- Called automatically before processing each request
- Only affects basic tier users

#### `performManualDailyReset(userId)`
- Manual reset function for testing/admin purposes
- Returns detailed result including success status and reset information

### Timezone Handling
- All date calculations use UTC timezone
- Reset occurs at 00:00 UTC regardless of server timezone
- Consistent behavior across different deployment environments

## Testing

### Test Script
Run the test script to validate the implementation:
```bash
cd affiliate-cms
node scripts/test-daily-reset.js
```

### Manual Testing Steps
1. Create a user with basic-month subscription
2. Make requests to increment the request count
3. Wait for the next day or manually trigger reset
4. Verify the count resets to 0
5. Verify non-basic tier users are not affected

### Test Scenarios
- ✅ Basic tier user gets daily reset
- ✅ Premium tier user does not get reset
- ✅ Reset only happens once per day
- ✅ Reset preserves request_limit and other data
- ✅ UTC timezone handling works correctly

## Logging

The system logs daily reset activities:
- `[Daily Reset] Performing daily reset for user tracking ID {id}`
- `[Daily Reset] Successfully reset user tracking ID {id}`
- `[Manual Daily Reset] Successfully reset user {userId}`

## Performance Considerations

- Reset check adds minimal overhead to each request
- Only basic tier users have the additional reset logic
- No background processes or scheduled jobs required
- Database updates are minimal (only when reset is needed)

## Backward Compatibility

- Existing users without `last_daily_reset_date` will get their first reset on next request
- All existing functionality remains unchanged
- Non-basic tier users experience no changes

## Configuration

No additional configuration is required. The feature works with:
- Existing subscription tier system
- Current user tracking infrastructure
- Standard Strapi authentication

## Monitoring

Monitor the feature by:
- Checking logs for daily reset activities
- Using the `/my-stats` endpoint to verify user request counts
- Monitoring database for `last_daily_reset_date` updates

## Future Enhancements

Potential improvements:
- Configurable reset time (not just 00:00 UTC)
- Different reset intervals for different tiers
- Reset statistics and analytics
- Bulk reset operations for administrators
