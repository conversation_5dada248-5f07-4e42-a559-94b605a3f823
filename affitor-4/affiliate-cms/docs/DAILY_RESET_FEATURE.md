# Daily Rate Limiting Reset Feature

## Overview

This feature implements automatic daily rate limiting resets for users subscribed to basic tier subscriptions. The system automatically resets the user's request count to 0 at the beginning of each day (00:00 UTC) for users with basic tier subscriptions only.

## Affected Subscription Tiers

The daily reset applies only to users with the following subscription tier names:
- `basic-month`
- `basic-quarter` 
- `basic-year`

Users with other subscription tiers (premium, pro, etc.) are **not affected** by daily resets.

## How It Works

### Automatic Reset Trigger
- The reset is triggered automatically on the first API request each day
- No background jobs or cron tasks are required
- Reset occurs at 00:00 UTC (start of day in UTC timezone)

### Reset Process
1. When a user makes a request, the system checks if they have a basic tier subscription
2. If they do, it checks if a daily reset is needed (no reset today or last reset was before today)
3. If reset is needed, it sets `request_count` to 0 and updates `last_daily_reset_date`
4. The request then proceeds normally with the reset count

### Data Preservation
- Only `request_count` is reset to 0
- `request_limit` is preserved from the subscription tier
- `statistics` and other tracking data are preserved
- `last_request_date` continues to track the most recent request

## Database Changes

### New Field Added
A new field `last_daily_reset_date` has been added to the `user_tracking_requests` table:

```json
{
  "last_daily_reset_date": {
    "type": "datetime",
    "description": "Last date when daily reset was performed for basic tier users"
  }
}
```

## API Endpoints

### Get User Stats
```
GET /api/user-tracking-request/my-stats
```
Returns current user's tracking statistics including request count, limit, and remaining requests.

### Manual Daily Reset (Admin Only)
```
POST /api/user-tracking-request/manual-daily-reset
Body: { "userId": 123 }
```
Allows administrators to manually trigger a daily reset for testing purposes.

## Implementation Details

### Service Methods

#### `isBasicTierUser(tracking)`
- Checks if a user has a basic tier subscription
- Returns `true` for basic-month, basic-quarter, basic-year tiers
- Returns `false` for all other tiers

#### `getStartOfDayUTC(date)`
- Helper function to get the start of day in UTC timezone
- Ensures consistent date handling across different server timezones

#### `checkAndPerformDailyReset(tracking)`
- Main logic for checking if reset is needed and performing it
- Called automatically before processing each request
- Only affects basic tier users

#### `performManualDailyReset(userId)`
- Manual reset function for testing/admin purposes
- Returns detailed result including success status and reset information

### Timezone Handling
- All date calculations use UTC timezone
- Reset occurs at 00:00 UTC regardless of server timezone
- Consistent behavior across different deployment environments

## Testing

### Test Script
Run the test script to validate the implementation:
```bash
cd affiliate-cms
node scripts/test-daily-reset.js
```

### Manual Testing Steps
1. Create a user with basic-month subscription
2. Make requests to increment the request count
3. Wait for the next day or manually trigger reset
4. Verify the count resets to 0
5. Verify non-basic tier users are not affected

### Test Scenarios
- ✅ Basic tier user gets daily reset
- ✅ Premium tier user does not get reset
- ✅ Reset only happens once per day
- ✅ Reset preserves request_limit and other data
- ✅ UTC timezone handling works correctly

## Logging

The system logs daily reset activities:
- `[Daily Reset] Performing daily reset for user tracking ID {id}`
- `[Daily Reset] Successfully reset user tracking ID {id}`
- `[Manual Daily Reset] Successfully reset user {userId}`

## Performance Considerations

- Reset check adds minimal overhead to each request
- Only basic tier users have the additional reset logic
- No background processes or scheduled jobs required
- Database updates are minimal (only when reset is needed)

## Backward Compatibility

- Existing users without `last_daily_reset_date` will get their first reset on next request
- All existing functionality remains unchanged
- Non-basic tier users experience no changes

## Configuration

No additional configuration is required. The feature works with:
- Existing subscription tier system
- Current user tracking infrastructure
- Standard Strapi authentication

## Monitoring

Monitor the feature by:
- Checking logs for daily reset activities
- Using the `/my-stats` endpoint to verify user request counts
- Monitoring database for `last_daily_reset_date` updates

## Future Enhancements

Potential improvements:
- Configurable reset time (not just 00:00 UTC)
- Different reset intervals for different tiers
- Reset statistics and analytics
- Bulk reset operations for administrators
