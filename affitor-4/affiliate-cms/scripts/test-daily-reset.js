/**
 * Test script for daily reset functionality
 * Run with: node scripts/test-daily-reset.js
 */

const { createStrapi } = require('@strapi/strapi');

async function testDailyReset() {
  console.log('🧪 Starting daily reset test...');
  
  // Initialize Strapi
  const strapi = await createStrapi().load();
  
  try {
    const userTrackingService = strapi.service('api::user-tracking-request.user-tracking-request');
    const subscriptionTierService = strapi.service('api::subscription-tier.subscription-tier');
    
    console.log('\n📋 Test Plan:');
    console.log('1. Create a test user with basic-month subscription');
    console.log('2. Make some requests to increment count');
    console.log('3. Test daily reset functionality');
    console.log('4. Verify reset only affects basic tier users');
    
    // Test 1: Check if basic tier detection works
    console.log('\n🔍 Test 1: Basic tier detection');
    
    // Get basic monthly tier
    const basicTier = await subscriptionTierService.getBasicMonthlyTier();
    if (!basicTier) {
      console.log('❌ No basic-month tier found. Please create one first.');
      return;
    }
    
    console.log('✅ Found basic tier:', {
      name: basicTier.name,
      id: basicTier.documentId || basicTier.id,
      request_limit: basicTier.request_limit
    });
    
    // Test 2: Test helper functions
    console.log('\n🔍 Test 2: Helper functions');
    
    const todayUTC = userTrackingService.getStartOfDayUTC();
    console.log('✅ UTC start of day:', todayUTC.toISOString());
    
    // Test 3: Mock tracking record for basic tier user
    console.log('\n🔍 Test 3: Basic tier user detection');
    
    const mockBasicTracking = {
      id: 999,
      subscription_tier: {
        name: 'basic-month',
        request_limit: 100
      },
      request_count: 50,
      last_daily_reset_date: null
    };
    
    const isBasicTier = await userTrackingService.isBasicTierUser(mockBasicTracking);
    console.log('✅ Basic tier detection result:', isBasicTier);
    
    // Test 4: Mock tracking record for non-basic tier user
    console.log('\n🔍 Test 4: Non-basic tier user detection');
    
    const mockPremiumTracking = {
      id: 998,
      subscription_tier: {
        name: 'premium-month',
        request_limit: 1000
      },
      request_count: 50,
      last_daily_reset_date: null
    };
    
    const isPremiumBasicTier = await userTrackingService.isBasicTierUser(mockPremiumTracking);
    console.log('✅ Premium tier detection result (should be false):', isPremiumBasicTier);
    
    // Test 5: Date comparison logic
    console.log('\n🔍 Test 5: Date comparison logic');
    
    const yesterday = new Date();
    yesterday.setUTCDate(yesterday.getUTCDate() - 1);
    
    const yesterdayUTC = userTrackingService.getStartOfDayUTC(yesterday);
    const needsReset = yesterdayUTC.getTime() < todayUTC.getTime();
    
    console.log('✅ Yesterday UTC:', yesterdayUTC.toISOString());
    console.log('✅ Today UTC:', todayUTC.toISOString());
    console.log('✅ Needs reset (should be true):', needsReset);
    
    console.log('\n✅ All tests completed successfully!');
    console.log('\n📝 Implementation Summary:');
    console.log('- Daily reset triggers automatically on first request each day');
    console.log('- Only affects users with basic-month, basic-quarter, basic-year tiers');
    console.log('- Resets request_count to 0 at 00:00 UTC');
    console.log('- Preserves request_limit and other user data');
    console.log('- Tracks last reset date to prevent multiple resets per day');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await strapi.destroy();
  }
}

// Run the test
testDailyReset().catch(console.error);
